module.exports = {
  // Umi Max 项目
  extends: [require.resolve('@umijs/max/eslint')],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: './tsconfig.json',
    tsconfigRootDir: './',
  },
  plugins: ['simple-import-sort'],
  settings: {
    'import/resolver': {
      typescript: {
        project: './tsconfig.json',
      },
    },
  },
  ignorePatterns: ['/externals'],
  rules: {
    // 添加这条规则来禁止字符串拼接，推荐使用模板字符串
    'prefer-template': 'error',
    'no-useless-concat': 'error', // 禁止不必要的字符串字面量或模板字面量的连接
    'prefer-const': 'warn',
    'new-cap': ['error', { capIsNew: false, newIsCap: true, properties: true }],
    'no-console': ['warn', { allow: ['info', 'warn', 'error', 'debug'] }],
    'no-restricted-syntax': [
      'error',
      'DebuggerStatement',
      'LabeledStatement',
      'WithStatement',
      'TSEnumDeclaration[const=true]',
      'TSExportAssignment',
    ],
    'prefer-arrow-callback': ['error', { allowNamedFunctions: true, allowUnboundThis: true }],
    'simple-import-sort/imports': 'error',
    'simple-import-sort/exports': 'error',

    // React Hooks 相关规则
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    'react/jsx-no-constructed-context-values': 'error',
    'react/jsx-no-useless-fragment': 'warn',
    'react/no-array-index-key': 'warn',
    'react/no-danger': 'error',
    'react/no-deprecated': 'error',
    'react/no-direct-mutation-state': 'error',
    'react/no-find-dom-node': 'error',
    'react/no-is-mounted': 'error',
    'react/no-render-return-value': 'error',
    'react/no-string-refs': 'error',
    'react/no-unknown-property': 'error',
    'react/require-render-return': 'error',
    'react/void-dom-elements-no-children': 'error',

    // TypeScript 相关规则
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/consistent-type-imports': ['error', { prefer: 'type-imports' }],
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/prefer-optional-chain': 'warn',
    '@typescript-eslint/ban-ts-comment': ['warn', { 'ts-expect-error': 'allow-with-description' }],

    // React 相关规则
    'react/jsx-no-duplicate-props': 'error',
    'react/jsx-pascal-case': 'error',
    'react/jsx-sort-props': [
      'error',
      {
        callbacksLast: true,
        shorthandFirst: true,
        ignoreCase: true,
        reservedFirst: true,
      },
    ],
    'react/no-unused-prop-types': 'warn',
    'react/self-closing-comp': 'error',

    // 代码风格和质量规则
    'no-var': 'error',
    'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    'comma-dangle': ['error', 'always-multiline'],
    semi: ['error', 'always'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'no-unneeded-ternary': 'error',
    'no-param-reassign': 'error',
    'no-return-assign': 'error',
    'no-sequences': 'error',
    'no-unused-expressions': 'warn',
    'no-void': 'error',
    'no-useless-constructor': 'error',
    'no-useless-rename': 'error',
    'operator-assignment': ['error', 'always'],
    'prefer-spread': 'error',
    'prefer-rest-params': 'error',
    'prefer-object-spread': 'error',
    'no-useless-escape': 'error',
    'no-useless-return': 'error',
    'no-useless-catch': 'error',
    'no-useless-computed-key': 'error',
    'no-useless-call': 'error',
    'no-constant-condition': 'error', // 禁止在条件语句中使用常量
    'no-duplicate-case': 'error', // 禁止 switch 语句中的重复 case
    'no-empty': ['error', { allowEmptyCatch: true }], // 禁止空块语句
    'no-extra-boolean-cast': 'error', // 禁止不必要的布尔类型转换
    'no-extra-semi': 'error', // 禁止多余的分号

    // 'no-await-in-loop': 'error', // 禁止在循环中使用 await
    'no-async-promise-executor': 'error', // 禁止在 Promise 构造函数中使用 async
    // 'require-await': 'error', // 要求 async 函数中有 await
  },
};
